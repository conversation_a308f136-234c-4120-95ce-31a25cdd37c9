package cn.iocoder.yudao.module.infra.service.demo;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;

import cn.iocoder.yudao.module.infra.controller.admin.demo.vo.*;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraStudentDO;
import cn.iocoder.yudao.module.infra.dal.mysql.demo.InfraStudentMapper;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static cn.iocoder.yudao.module.infra.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.*;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.*;
import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.*;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.*;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link InfraStudentServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(InfraStudentServiceImpl.class)
public class InfraStudentServiceImplTest extends BaseDbUnitTest {

    @Resource
    private InfraStudentServiceImpl studentService;

    @Resource
    private InfraStudentMapper studentMapper;

    @Test
    public void testCreateStudent_success() {
        // 准备参数
        InfraStudentSaveReqVO createReqVO = randomPojo(InfraStudentSaveReqVO.class).setId(null);

        // 调用
        Long studentId = studentService.createStudent(createReqVO);
        // 断言
        assertNotNull(studentId);
        // 校验记录的属性是否正确
        InfraStudentDO student = studentMapper.selectById(studentId);
        assertPojoEquals(createReqVO, student, "id");
    }

    @Test
    public void testUpdateStudent_success() {
        // mock 数据
        InfraStudentDO dbStudent = randomPojo(InfraStudentDO.class);
        studentMapper.insert(dbStudent);// @Sql: 先插入出一条存在的数据
        // 准备参数
        InfraStudentSaveReqVO updateReqVO = randomPojo(InfraStudentSaveReqVO.class, o -> {
            o.setId(dbStudent.getId()); // 设置更新的 ID
        });

        // 调用
        studentService.updateStudent(updateReqVO);
        // 校验是否更新正确
        InfraStudentDO student = studentMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, student);
    }

    @Test
    public void testUpdateStudent_notExists() {
        // 准备参数
        InfraStudentSaveReqVO updateReqVO = randomPojo(InfraStudentSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> studentService.updateStudent(updateReqVO), STUDENT_NOT_EXISTS);
    }

    @Test
    public void testDeleteStudent_success() {
        // mock 数据
        InfraStudentDO dbStudent = randomPojo(InfraStudentDO.class);
        studentMapper.insert(dbStudent);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbStudent.getId();

        // 调用
        studentService.deleteStudent(id);
       // 校验数据不存在了
       assertNull(studentMapper.selectById(id));
    }

    @Test
    public void testDeleteStudent_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> studentService.deleteStudent(id), STUDENT_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetStudentPage() {
       // mock 数据
       InfraStudentDO dbStudent = randomPojo(InfraStudentDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setBirthday(null);
           o.setSex(null);
           o.setEnabled(null);
           o.setCreateTime(null);
       });
       studentMapper.insert(dbStudent);
       // 测试 name 不匹配
       studentMapper.insert(cloneIgnoreId(dbStudent, o -> o.setName(null)));
       // 测试 birthday 不匹配
       studentMapper.insert(cloneIgnoreId(dbStudent, o -> o.setBirthday(null)));
       // 测试 sex 不匹配
       studentMapper.insert(cloneIgnoreId(dbStudent, o -> o.setSex(null)));
       // 测试 enabled 不匹配
       studentMapper.insert(cloneIgnoreId(dbStudent, o -> o.setEnabled(null)));
       // 测试 createTime 不匹配
       studentMapper.insert(cloneIgnoreId(dbStudent, o -> o.setCreateTime(null)));
       // 准备参数
       InfraStudentPageReqVO reqVO = new InfraStudentPageReqVO();
       reqVO.setName(null);
       reqVO.setBirthday(null);
       reqVO.setSex(null);
       reqVO.setEnabled(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<InfraStudentDO> pageResult = studentService.getStudentPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbStudent, pageResult.getList().get(0));
    }

}