<template>
  <div class="app-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        v-loading="formLoading"
        label-width="0px"
        :inline-message="true"
      >
        <el-table :data="formData" class="-mt-10px">
          <el-table-column label="序号" type="index" width="100" />
                       <el-table-column label="名字" min-width="150">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.name`" :rules="formRules.name" class="mb-0px!">
                            <el-input v-model="row.name" placeholder="请输入名字" />
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="简介" min-width="200">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.description`" :rules="formRules.description" class="mb-0px!">
                            <el-input v-model="row.description" type="textarea" placeholder="请输入简介" />
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="出生日期" min-width="150">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.birthday`" :rules="formRules.birthday" class="mb-0px!">
                            <el-date-picker clearable v-model="row.birthday" type="date" value-format="timestamp" placeholder="选择出生日期" />
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="性别" min-width="150">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.sex`" :rules="formRules.sex" class="mb-0px!">
                            <el-select v-model="row.sex" placeholder="请选择性别">
                                  <el-option v-for="dict in this.getDictDatas(DICT_TYPE.SYSTEM_USER_SEX)"
                                             :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="是否有效" min-width="150">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.enabled`" :rules="formRules.enabled" class="mb-0px!">
                            <el-radio-group v-model="row.enabled">
                                  <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
                                            :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="头像" min-width="200">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.avatar`" :rules="formRules.avatar" class="mb-0px!">
                            <ImageUpload v-model="row.avatar"/>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="附件" min-width="200">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.video`" :rules="formRules.video" class="mb-0px!">
                            <FileUpload v-model="row.video"/>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="备注" min-width="400">
                        <template v-slot="{ row, $index }">
                          <el-form-item :prop="`${$index}.memo`" :rules="formRules.memo" class="mb-0px!">
                            <Editor v-model="row.memo" :min-height="192"/>
                          </el-form-item>
                        </template>
                      </el-table-column>
          <el-table-column align="center" fixed="right" label="操作" width="60">
            <template v-slot="{ $index }">
              <el-link @click="handleDelete($index)">—</el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <el-row justify="center" class="mt-3">
        <el-button @click="handleAdd" round>+ 添加学生联系人</el-button>
      </el-row>
  </div>
</template>

<script>
  import * as StudentApi from '@/api/infra/demo';
      import ImageUpload from '@/components/ImageUpload';
      import FileUpload from '@/components/FileUpload';
      import Editor from '@/components/Editor';
  export default {
    name: "StudentContactForm",
    components: {
          ImageUpload,
          FileUpload,
          Editor,
    },
    props:[
      'studentId'
    ],// 学生编号（主表的关联字段）
    data() {
      return {
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: [],
        // 表单校验
        formRules: {
                        studentId: [{ required: true, message: "学生编号不能为空", trigger: "blur" }],
                        name: [{ required: true, message: "名字不能为空", trigger: "blur" }],
                        description: [{ required: true, message: "简介不能为空", trigger: "blur" }],
                        birthday: [{ required: true, message: "出生日期不能为空", trigger: "blur" }],
                        sex: [{ required: true, message: "性别不能为空", trigger: "change" }],
                        enabled: [{ required: true, message: "是否有效不能为空", trigger: "blur" }],
                        avatar: [{ required: true, message: "头像不能为空", trigger: "blur" }],
                        memo: [{ required: true, message: "备注不能为空", trigger: "blur" }],
        },
      };
    },
    watch:{/** 监听主表的关联字段的变化，加载对应的子表数据 */
      studentId:{
        handler(val) {
          // 1. 重置表单
              this.formData = []
          // 2. val 非空，则加载数据
          if (!val) {
            return;
          }
          try {
            this.formLoading = true;
            // 这里还是需要获取一下 this 的不然取不到 formData
            const that = this;
            StudentApi.getStudentContactListByStudentId(val).then(function (res){
              that.formData = res.data;
            })
          } finally {
            this.formLoading = false;
          }
        },
        immediate: true
      }
    },
    methods: {
          /** 新增按钮操作 */
          handleAdd() {
            const row = {
                                id: undefined,
                                studentId: undefined,
                                name: undefined,
                                description: undefined,
                                birthday: undefined,
                                sex: undefined,
                                enabled: undefined,
                                avatar: undefined,
                                video: undefined,
                                memo: undefined,
            }
            row.studentId = this.studentId;
            this.formData.push(row);
          },
          /** 删除按钮操作 */
          handleDelete(index) {
            this.formData.splice(index, 1);
          },
      /** 表单校验 */
      validate(){
        return this.$refs["formRef"].validate();
      },
      /** 表单值 */
      getData(){
        return this.formData;
      }
    }
  };
</script>
