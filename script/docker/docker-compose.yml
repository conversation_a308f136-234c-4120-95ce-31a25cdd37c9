version: '3'
services:
  yudao-gateway:
    image: yudao-gateway
    container_name: yudao-gateway
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=yudao-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/yudao-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host # 以主机网络环境运行
  yudao-system:
    image: yudao-module-system-biz
    container_name: yudao-system
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=yudao-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/yudao-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    healthcheck:
      test: [ "CMD","curl","-f","http://localhost:48081" ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: always
    network_mode: host
  yudao-infra:
    image: yudao-module-infra-biz
    container_name: yudao-infra
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=yudao-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/yudao-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    healthcheck:
      test: [ "CMD","curl","-f","http://localhost:48082" ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    depends_on:
      yudao-system:
        condition: service_healthy
  yudao-report:
    image: yudao-module-report-biz
    container_name: yudao-report
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=yudao-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/yudao-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    depends_on:
      yudao-infra:
        condition: service_healthy
  yudao-bpm:
    image: yudao-module-bpm-biz
    container_name: yudao-bpm
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=yudao-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/yudao-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    depends_on:
      yudao-infra:
        condition: service_healthy
  yudao-pay:
    image: yudao-module-pay-biz
    container_name: yudao-pay
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=yudao-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/yudao-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    depends_on:
      yudao-infra:
        condition: service_healthy
  yudao-mp:
    image: yudao-module-mp-biz
    container_name: yudao-mp
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=yudao-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/yudao-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    depends_on:
      yudao-infra:
        condition: service_healthy