<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.crm.dal.mysql.statistics.CrmStatisticsPerformanceMapper">

    <select id="selectContractCountPerformance"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.performance.CrmStatisticsPerformanceRespVO">
        SELECT
            DATE_FORMAT(order_date, '%Y%m') AS time,
            COUNT(1) AS currentMonthCount
        FROM crm_contract
        WHERE deleted = 0
        AND audit_status = ${@<EMAIL>}
        AND owner_user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        AND order_date between #{times[0],javaType=java.time.LocalDateTime} and
            #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY time
    </select>

    <select id="selectContractPricePerformance"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.performance.CrmStatisticsPerformanceRespVO">
        SELECT
            DATE_FORMAT(order_date, '%Y%m') AS time,
            IFNULL(SUM(total_price), 0) AS currentMonthCount
        FROM crm_contract
        WHERE deleted = 0
        AND audit_status = ${@<EMAIL>}
        AND owner_user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        AND order_date between #{times[0],javaType=java.time.LocalDateTime} and
            #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY time
    </select>

    <select id="selectReceivablePricePerformance"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.performance.CrmStatisticsPerformanceRespVO">
        SELECT
            DATE_FORMAT(return_time, '%Y%m') AS time,
            IFNULL(SUM(price), 0) AS currentMonthCount
        FROM crm_receivable
        WHERE deleted = 0
        AND audit_status = ${@<EMAIL>}
        AND owner_user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        AND return_time between #{times[0],javaType=java.time.LocalDateTime} and
            #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY time
    </select>

</mapper>
