<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.crm.dal.mysql.statistics.CrmStatisticsFunnelMapper">

    <select id="selectCustomerCountByDate" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM crm_customer
        WHERE deleted = 0
        AND owner_user_id IN
        <!-- TODO @puhui999：这个 foreach 搞个缩进哈 - -->
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND create_time BETWEEN #{times[0],javaType=java.time.LocalDateTime} AND
        <!-- TODO @puhui999：下面这个，就不缩进啦 - -->
        #{times[1],javaType=java.time.LocalDateTime}
    </select>

    <select id="selectBusinessCountByDateAndEndStatus" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM crm_business
        WHERE deleted = 0
        <if test="status != null">
            AND end_status = #{status}
        </if>
        AND owner_user_id IN
        <foreach collection="reqVO.userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND create_time BETWEEN #{reqVO.times[0],javaType=java.time.LocalDateTime} AND
        #{reqVO.times[1],javaType=java.time.LocalDateTime}
    </select>

    <select id="selectBusinessSummaryListGroupByEndStatus"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.funnel.CrmStatisticsBusinessSummaryByEndStatusRespVO">
        SELECT
            end_status AS endStatus,
            COUNT(*) AS businessCount,
            SUM(total_price) AS totalPrice
        FROM crm_business
        WHERE deleted = 0 AND end_status IS NOT NULL
        AND owner_user_id IN
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND create_time BETWEEN #{times[0],javaType=java.time.LocalDateTime} AND
        #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY end_status
    </select>

    <select id="selectBusinessSummaryGroupByDate"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.funnel.CrmStatisticsBusinessSummaryByDateRespVO">
        SELECT
            DATE_FORMAT(create_time, '%Y-%m-%d') AS time,
            COUNT(*) AS businessCreateCount,
            SUM(total_price) AS totalPrice
        FROM crm_business
        WHERE deleted = 0
        AND owner_user_id IN
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND create_time BETWEEN #{times[0],javaType=java.time.LocalDateTime} AND
        #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY time
    </select>

    <select id="selectBusinessInversionRateSummaryByDate"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.funnel.CrmStatisticsBusinessInversionRateSummaryByDateRespVO">
        SELECT
            DATE_FORMAT(create_time, '%Y-%m-%d') AS time,
            COUNT(*) AS businessCount,
            SUM(IF(end_status = 1, 1, 0)) AS businessWinCount
        FROM crm_business
        WHERE deleted = 0
        AND owner_user_id IN
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND create_time BETWEEN #{times[0],javaType=java.time.LocalDateTime} AND
        #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY time
    </select>

</mapper>
