<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.crm.dal.mysql.statistics.CrmStatisticsPortraitMapper">

    <select id="selectSummaryListGroupByAreaId"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.portrait.CrmStatisticCustomerAreaRespVO">
        SELECT area_id, COUNT(*) AS customerCount, SUM(deal_status) AS dealCount
        FROM crm_customer
        WHERE deleted = 0 AND area_id IS NOT NULL
        AND owner_user_id IN
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND create_time BETWEEN #{times[0],javaType=java.time.LocalDateTime}
        AND #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY area_id
    </select>

    <select id="selectCustomerIndustryListGroupByIndustryId"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.portrait.CrmStatisticCustomerIndustryRespVO">
        SELECT industry_id, COUNT(*) AS customerCount, SUM(deal_status) AS dealCount
        FROM crm_customer
        WHERE deleted = 0 AND industry_id IS NOT NULL
        AND owner_user_id IN
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND create_time BETWEEN #{times[0],javaType=java.time.LocalDateTime}
        AND #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY industry_id
    </select>

    <select id="selectCustomerSourceListGroupBySource"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.portrait.CrmStatisticCustomerSourceRespVO">
        SELECT source, COUNT(*) AS customerCount, SUM(deal_status) AS dealCount
        FROM crm_customer
        WHERE deleted = 0 AND source IS NOT NULL
        AND owner_user_id IN
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND create_time BETWEEN #{times[0],javaType=java.time.LocalDateTime}
        AND #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY source
    </select>

    <select id="selectCustomerLevelListGroupByLevel"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.portrait.CrmStatisticCustomerLevelRespVO">
        SELECT level, COUNT(*) AS customerCount, SUM(deal_status) AS dealCount
        FROM crm_customer
        WHERE deleted = 0 AND level IS NOT NULL
        AND owner_user_id IN
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND create_time BETWEEN #{times[0],javaType=java.time.LocalDateTime}
        AND #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY level
    </select>

</mapper>
